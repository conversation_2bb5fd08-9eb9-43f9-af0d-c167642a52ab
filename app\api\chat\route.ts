import { z } from "zod"
import { openai } from "@ai-sdk/openai"
import { streamText, convertToCoreMessages, type UIMessage } from "ai"
import { getDb } from "@/lib/mongodb"

export const maxDuration = 30

const ListingsFilterSchema = z.object({
  location: z.string().optional(),
  society: z.string().optional(),
  cluster: z.string().optional(),
  minRooms: z.number().int().min(1).optional(),
  maxRooms: z.number().int().min(1).optional(),
  baths: z.number().int().min(1).optional(),
  balconies: z.number().int().min(0).optional(),
  minSize: z.number().int().min(0).optional(),
  maxSize: z.number().int().min(0).optional(),
  furnishing: z.enum(["Full", "Semi", "None"]).optional(),
  floorMin: z.number().int().min(0).optional(),
  floorMax: z.number().int().min(0).optional(),
  facing: z.string().optional(),
  balconyFacing: z.string().optional(),
  minRent: z.number().int().min(0).optional(),
  maxRent: z.number().int().min(0).optional(),
  maintenanceMax: z.number().int().min(0).optional(),
  availableFrom: z.string().optional(),
  tenantType: z.enum(["All", "Family Only"]).optional(),
  sortBy: z.enum(["rent", "size", "floor", "societyAge"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
})

type ListingsFilter = z.infer<typeof ListingsFilterSchema>

type Listing = {
  id?: string
  heliumNameOrId?: string
  society: string
  cluster?: string
  rooms: number
  baths: number
  balconies: number
  size: number
  furnishing: "Full" | "Semi" | "None"
  floor: number
  totalFloors?: number
  facing?: string
  balconyFacing?: string
  rent: number
  maintenance?: number
  totalRent?: number
  societyAge?: string
  availableFrom?: string
  tenantType?: string
}

async function fetchListings(filters: ListingsFilter): Promise<Listing[]> {
  const db = await getDb()
  const col = db.collection("listings")

  const q: Record<string, any> = {}
  const toRegex = (v: string, prefix = false) =>
    new RegExp(prefix ? `^${v.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}` : v.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "i")

  if (filters.society) q.society = { $regex: toRegex(filters.society, true) }
  if (filters.cluster) q.cluster = { $regex: toRegex(filters.cluster, true) }
  if (filters.location) q.location = { $regex: toRegex(filters.location, false) }
  if (filters.furnishing) q.furnishing = filters.furnishing
  if (filters.tenantType) q.tenantType = filters.tenantType
  if (filters.facing) q.facing = filters.facing
  if (filters.balconyFacing) q.balconyFacing = filters.balconyFacing
  if (filters.availableFrom) q.availableFrom = { $regex: toRegex(filters.availableFrom, false) }

  const addRange = (field: string, min?: number, max?: number) => {
    if (min == null && max == null) return
    q[field] = { ...(q[field] || {}), ...(min != null ? { $gte: min } : {}), ...(max != null ? { $lte: max } : {}) }
  }
  addRange("rooms", filters.minRooms, filters.maxRooms)
  if (filters.baths != null) q.baths = filters.baths
  if (filters.balconies != null) q.balconies = filters.balconies
  addRange("size", filters.minSize, filters.maxSize)
  addRange("floor", filters.floorMin, filters.floorMax)
  if (filters.minRent != null || filters.maxRent != null) addRange("rent", filters.minRent, filters.maxRent)
  if (filters.maintenanceMax != null) addRange("maintenance", undefined, filters.maintenanceMax)

  const docs = await col.find(q).limit(100).toArray()

  return docs.map((row: any) => ({
    id: row._id?.toString?.(),
    heliumNameOrId: row["Helium Name / ID"] ?? row.HeliumID ?? row.heliumNameOrId,
    society: row.Society ?? row.society ?? "",
    cluster: row.Cluster ?? row.cluster,
    rooms: Number(row["# Rooms"] ?? row.rooms ?? 0),
    baths: Number(row["# Bath"] ?? row.baths ?? 0),
    balconies: Number(row["# Balcony"] ?? row.balconies ?? 0),
    size: Number(row.Size ?? row.size ?? 0),
    furnishing: (row.Furnishing ?? row.furnishing ?? "None") as Listing["furnishing"],
    floor: Number(row.Floor ?? row.floor ?? 0),
    totalFloors: Number(row["Total Floors"] ?? row.totalFloors ?? 0) || undefined,
    facing: row.Facing ?? row.facing,
    balconyFacing: row["Balcony Facing"] ?? row.balconyFacing,
    rent: Number(String(row.Rent ?? row.rent ?? 0).replace(/[, ]/g, "")),
    maintenance: Number(String(row.Maintenance ?? row.maintenance ?? 0).replace(/[, ]/g, "")) || undefined,
    totalRent: Number(String(row["Total Rent"] ?? row.totalRent ?? 0).replace(/[, ]/g, "")) || undefined,
    societyAge: row["Society Age"] ?? row.societyAge,
    availableFrom: row["Available From"] ?? row.availableFrom,
    tenantType: row["Tenant Type"] ?? row.tenantType,
  }))
}

function listingsToText(list: Listing[]): string {
  if (list.length === 0) return "No matches found. Try adjusting budget, location, or rooms."
  return list
    .map((l, i) => {
      const total = l.totalRent ?? (l.maintenance ? l.rent + l.maintenance : l.rent)
      return [
        `#${i + 1} ${l.society}${l.cluster ? ` (${l.cluster})` : ""}`,
        `- ${l.rooms} rooms • ${l.baths} baths • ${l.balconies} balconies • ${l.size} sqft`,
        `- Furnishing: ${l.furnishing} • Floor: ${l.floor}${l.totalFloors ? `/${l.totalFloors}` : ""}`,
        l.facing ? `- Facing: ${l.facing}${l.balconyFacing ? `, Balcony: ${l.balconyFacing}` : ""}` : "",
        `- Rent: ₹${l.rent.toLocaleString()}${l.maintenance ? ` + Maint: ₹${l.maintenance.toLocaleString()}` : ""} → Total: ₹${total.toLocaleString()}`,
        `${l.availableFrom ? `- Available: ${l.availableFrom}` : ""} ${l.tenantType ? `• Tenant: ${l.tenantType}` : ""}`,
      ]
        .filter(Boolean)
        .join("\n")
    })
    .join("\n\n")
}

export async function POST(req: Request) {
  const { id, messages }: { id: string; messages: Array<UIMessage> } = await req.json()

  const coreMessages = convertToCoreMessages(messages).filter((m) => m.content.length > 0)

  let societies: string[] = []

  const result = await streamText({
    model: openai("gpt-4o"),
    system: "You are a Bangalore rental home finder...",
    messages: coreMessages,
    tools: {
      searchListings: {
        description: "Search rental listings",
        inputSchema: ListingsFilterSchema,
        async execute(filters) {
          const listings = await fetchListings(filters)
          const sorted = [...listings].sort((a, b) => {
            const ta = a.totalRent ?? (a.maintenance ? a.rent + a.maintenance : a.rent)
            const tb = b.totalRent ?? (b.maintenance ? b.rent + b.maintenance : b.rent)
            return ta - tb
          })
          const top = sorted.slice(0, 5)
          societies = Array.from(new Set(top.map((l) => l.society).filter(Boolean)))
          return {
            count: top.length,
            items: top,
            societies,
            markdown: listingsToText(top),
          }
        },
      },
    },
    onFinish: async () => {
      // Here you could persist chat, but also we want to return societies
    },
  })

  // Include societies along with AI stream
  return result.toTextStreamResponse({
    extras: { societies },
  })
}

