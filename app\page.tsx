"use client"

import { useChat } from "@ai-sdk/react"
import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"

type AnyPart = {
  type: string
  text?: string
  // Tool parts often include args/result, but shape can vary; use unknown with safe rendering.
  args?: unknown
  result?: unknown
  output?: unknown
  toolName?: string
}

export default function Home() {
  const { messages, isLoading, sendMessage } = useChat({ api: "/api/chat" })
  const [input, setInput] = useState("")
  const listRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: "smooth" })
  }, [messages])

  return (
    <main className="mx-auto max-w-2xl p-6">
      <header className="mb-6">
        <h1 className="text-2xl font-semibold text-balance">Find your dream home</h1>
        <p className="text-sm text-muted-foreground">
          Tell me your budget, rooms, location, furnishing, facing, and when you can move in.
        </p>
      </header>

      <Card className="mb-4 h-[60vh] overflow-hidden">
        <CardContent className="p-0 h-full">
          <div ref={listRef} className="h-full overflow-y-auto p-4 space-y-3">
            {messages.map((m) => (
              <div key={m.id} className={m.role === "user" ? "flex justify-end" : "flex justify-start"}>
                <div
                  className={
                    m.role === "user"
                      ? "rounded-lg bg-primary text-primary-foreground px-3 py-2 max-w-[80%]"
                      : "rounded-lg bg-muted px-3 py-2 max-w-[80%]"
                  }
                >
                  <MessageContent parts={(m as any).parts} content={(m as any).content} />
                </div>
              </div>
            ))}
            {isLoading && <div className="text-sm text-muted-foreground">Searching listings…</div>}
          </div>
        </CardContent>
      </Card>

      <form
        onSubmit={(e) => {
          e.preventDefault()
          const text = input.trim()
          if (!text) return
          sendMessage({ text })
          setInput("")
        }}
        className="flex items-center gap-2"
      >
        <Input
          value={input}
          onChange={(e) => setInput(e.currentTarget.value)}
          placeholder="e.g., 2BHK in Whitefield, budget ₹65k, semi-furnished, Oct move-in"
          aria-label="Message"
        />
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Thinking..." : "Send"}
        </Button>
      </form>
    </main>
  )
}

function MessageContent({ parts, content }: { parts?: any[]; content?: string }) {
  if (Array.isArray(parts) && parts.length > 0) {
    return (
      <div className="space-y-2">
        {parts.map((part, i) => {
          // Assistant/User text chunks
          if (part.type === "text" && typeof part.text === "string") {
            return (
              <p key={i} className="text-pretty text-sm leading-6">
                {part.text}
              </p>
            )
          }

          // Tool results: part.type like "tool-searchListings"
          if (typeof part.type === "string" && part.type.startsWith("tool-")) {
            // Prefer result/output fields where the tool returned JSON
            const payload = part?.result !== undefined ? part.result : part?.output !== undefined ? part.output : part

            return (
              <pre
                key={i}
                className="text-xs whitespace-pre-wrap bg-background/60 border border-border rounded-md p-2 overflow-x-auto"
                aria-label="Tool result JSON"
              >
                {safeStringify(payload, 2)}
              </pre>
            )
          }

          // Unknown part; render safely
          return (
            <pre key={i} className="text-xs whitespace-pre-wrap">
              {safeStringify(part, 2)}
            </pre>
          )
        })}
      </div>
    )
  }

  // Fallback for legacy message shape with content string only
  if (typeof content === "string") {
    // Simple formatter: recognize small JSON blobs for tool results.
    if (content.trim().startsWith("{") || content.trim().startsWith("[")) {
      return <pre className="text-xs whitespace-pre-wrap">{content}</pre>
    }
    return <p className="text-pretty text-sm leading-6">{content}</p>
  }

  return null
}

function safeStringify(obj: unknown, spaces = 2): string {
  try {
    return JSON.stringify(obj, null, spaces)
  } catch {
    return String(obj)
  }
}
